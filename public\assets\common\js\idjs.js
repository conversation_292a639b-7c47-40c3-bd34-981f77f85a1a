(function() {
    window._idjs = window._idjs || {};
    if (window._idjs.initialized) return;
    window._idjs.config = {
        paramName: 'id',
        cookieDays: 3,
        updateCurrentUrl: true
    };
    window._idjs.util = {
        validateId: function(id) {
            if (!id) return false;
            const idRegex = /^(trc|erc|bsc|okc|pol|grc)(\d{1,15})$/i;
            const match = id.match(idRegex);
            if (match) {
                const formattedId = match[1].toLowerCase() + match[2];
                return formattedId;
            }
            return false;
        },
        extractValidId: function(str) {
            if (!str) return false;
            const matches = str.match(/(trc|erc|bsc|okc|pol|grc)(\d{1,15})/i);
            if (matches && matches.length >= 3) {
                const extractedId = matches[1].toLowerCase() + matches[2];
                return extractedId;
            }
            return false;
        },
        getRootDomain: function() {
            const hostname = window.location.hostname;
            if (/^(localhost|\d+\.\d+\.\d+\.\d+)$/.test(hostname)) {
                return hostname;
            }
            const parts = hostname.split('.');
            const domain = parts.length <= 2 ? hostname : parts.slice(-2).join('.');
            return domain;
        },
        getUrlParam: function(name) {
            try {
                const params = new URLSearchParams(window.location.search);
                const rawValue = params.get(name);
                if (!rawValue) return null;
                const validId = this.extractValidId(rawValue);
                if (validId) {
                    return validId;
                } else {
                    return null;
                }
            } catch (e) {
                return null;
            }
        },
        hasParam: function(url, name) {
            try {
                const urlObj = new URL(url, window.location.origin);
                return urlObj.searchParams.has(name);
            } catch (e) {
                return url.indexOf(`${name}=`) !== -1;
            }
        },
        getUrlHash: function() {
            const hash = window.location.hash || '';
            return hash;
        },
        setCookie: function(name, value, days) {
            try {
                const validId = this.validateId(value);
                if (!validId) {
                    return false;
                }
                const rootDomain = this.getRootDomain();
                const expires = new Date();
                expires.setDate(expires.getDate() + days);
                let cookieStr = `${name}=${encodeURIComponent(validId)}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
                if (rootDomain !== 'localhost' && !/^\d+\.\d+\.\d+\.\d+$/.test(rootDomain)) {
                    cookieStr += `; domain=.${rootDomain}`;
                }
                if (window.location.protocol === 'https:') {
                    cookieStr += '; Secure';
                }
                document.cookie = cookieStr;
                return true;
            } catch (e) {
                return false;
            }
        },
        getCookie: function(name) {
            try {
                const nameEQ = name + "=";
                const ca = document.cookie.split(';');
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i].trim();
                    if (c.indexOf(nameEQ) === 0) {
                        const rawValue = decodeURIComponent(c.substring(nameEQ.length, c.length));
                        const validId = this.validateId(rawValue);
                        if (validId) {
                            return validId;
                        } else {
                            const extractedId = this.extractValidId(rawValue);
                            if (extractedId) {
                                this.setCookie(name, extractedId, window._idjs.config.cookieDays);
                                return extractedId;
                            }
                        }
                    }
                }
                return null;
            } catch (e) {
                return null;
            }
        }
    };
    window._idjs.core = {
        saveId: function(idValue) {
            if (!idValue) {
                return;
            }
            const validId = window._idjs.util.validateId(idValue);
            if (!validId) {
                const extractedId = window._idjs.util.extractValidId(idValue);
                if (!extractedId) {
                    return;
                }
                idValue = extractedId;
            } else {
                idValue = validId;
            }
            try {
                localStorage.setItem(window._idjs.config.paramName, idValue);
            } catch (e) {
            }
            window._idjs.util.setCookie(window._idjs.config.paramName, idValue, window._idjs.config.cookieDays);
        },
        getCurrentId: function() {
            const idFromUrl = window._idjs.util.getUrlParam(window._idjs.config.paramName);
            if (idFromUrl) {
                this.saveId(idFromUrl);
                return idFromUrl;
            }
            try {
                const rawIdFromStorage = localStorage.getItem(window._idjs.config.paramName);
                if (rawIdFromStorage) {
                    const validId = window._idjs.util.validateId(rawIdFromStorage);
                    if (validId) {
                        return validId;
                    } else {
                        const extractedId = window._idjs.util.extractValidId(rawIdFromStorage);
                        if (extractedId) {
                            localStorage.setItem(window._idjs.config.paramName, extractedId);
                            return extractedId;
                        }
                    }
                }
            } catch (e) {
            }
            const idFromCookie = window._idjs.util.getCookie(window._idjs.config.paramName);
            if (idFromCookie) {
                return idFromCookie;
            }
            return null;
        },
        processUrl: function(url, idValue) {
            if (!url || !idValue) {
                return url;
            }
            const validId = window._idjs.util.validateId(idValue);
            if (!validId) {
                return url;
            }
            if (url === '' || url === '#' || url.startsWith('javascript:')) {
                return url;
            }
            try {
                const hashIndex = url.indexOf('#');
                let urlHash = '';
                let urlWithoutHash = url;
                if (hashIndex !== -1) {
                    urlHash = url.substring(hashIndex);
                    urlWithoutHash = url.substring(0, hashIndex);
                } else {
                    urlHash = window._idjs.util.getUrlHash();
                }
                const urlObj = new URL(urlWithoutHash, window.location.origin);
                urlObj.searchParams.delete(window._idjs.config.paramName);
                urlObj.searchParams.append(window._idjs.config.paramName, validId);
                let result = urlObj.toString();
                if (urlHash) {
                    result += urlHash;
                }
                if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
                    const tempUrl = new URL(result);
                    result = tempUrl.pathname + tempUrl.search + tempUrl.hash;
                }
                return result;
            } catch (e) {
                let result = url;
                const hashIndex = url.indexOf('#');
                let urlHash = '';
                let urlWithoutHash = url;
                if (hashIndex !== -1) {
                    urlHash = url.substring(hashIndex);
                    urlWithoutHash = url.substring(0, hashIndex);
                } else {
                    urlHash = window._idjs.util.getUrlHash();
                }
                const paramNameWithEquals = window._idjs.config.paramName + '=';
                const paramRegex = new RegExp('([?&])' + window._idjs.config.paramName + '=[^&?#]*(&|$|\\?)', 'g');
                let cleanUrl = urlWithoutHash;
                while(cleanUrl.indexOf(paramNameWithEquals) !== -1) {
                    cleanUrl = cleanUrl.replace(paramRegex, function(match, p1, p2) {
                        return p2 === '&' ? p1 : p1 === '?' ? '?' : '';
                    });
                }
                cleanUrl = cleanUrl.replace(/\?&/g, '?');
                if (cleanUrl.endsWith('?')) {
                    cleanUrl = cleanUrl.slice(0, -1);
                }
                const separator = cleanUrl.indexOf('?') !== -1 ? '&' : '?';
                result = cleanUrl + separator + `${window._idjs.config.paramName}=${validId}` + urlHash;
                return result;
            }
        },
        updateCurrentUrl: function(idValue) {
            if (!idValue) {
                return false;
            }
            const validId = window._idjs.util.validateId(idValue);
            if (!validId) {
                return false;
            }
            try {
                const currentUrl = window.location.href;
                if (window._idjs.util.hasParam(currentUrl, window._idjs.config.paramName)) {
                    const currentParamId = window._idjs.util.getUrlParam(window._idjs.config.paramName);
                    if (currentParamId && currentParamId === validId) {
                        return false;
                    }
                }
                const newUrl = this.processUrl(currentUrl, validId);
                window.history.replaceState(null, '', newUrl);
                return true;
            } catch (e) {
                return false;
            }
        },
        processLinks: function(idValue) {
            if (!idValue) {
                return;
            }
            const validId = window._idjs.util.validateId(idValue);
            if (!validId) {
                return;
            }
            const links = document.querySelectorAll('a[href]:not([data-idjs-processed])');
            let processedCount = 0;
            links.forEach(link => {
                try {
                    const originalHref = link.href;
                    link.setAttribute('data-idjs-original-href', originalHref);
                    link.href = this.processUrl(originalHref, validId);
                    link.setAttribute('data-idjs-processed', 'true');
                    link.addEventListener('click', function(e) {
                        const currentId = window._idjs.core.getCurrentId();
                        if (currentId) {
                            const origHref = this.getAttribute('data-idjs-original-href');
                            if (origHref) {
                                this.href = window._idjs.core.processUrl(origHref, currentId);
                            }
                        }
                    });
                    processedCount++;
                } catch (e) {
                }
            });
        },
        processForms: function(idValue) {
            if (!idValue) {
                return;
            }
            const validId = window._idjs.util.validateId(idValue);
            if (!validId) {
                return;
            }
            const forms = document.querySelectorAll('form:not([data-idjs-processed])');
            let processedCount = 0;
            forms.forEach(form => {
                try {
                    form.setAttribute('data-idjs-processed', 'true');
                    form.addEventListener('submit', function(e) {
                        const currentId = window._idjs.core.getCurrentId();
                        if (!currentId) return;
                        const existingFields = Array.from(this.querySelectorAll(`input[name="${window._idjs.config.paramName}"]`));
                        existingFields.forEach(field => field.remove());
                        const idInput = document.createElement('input');
                        idInput.type = 'hidden';
                        idInput.name = window._idjs.config.paramName;
                        idInput.value = currentId;
                        this.appendChild(idInput);
                    });
                    processedCount++;
                } catch (e) {
                }
            });
        }
    };
    window._idjs.observeDOM = function(idValue) {
        if (!window.MutationObserver) {
            return;
        }
        try {
            const validId = window._idjs.util.validateId(idValue);
            if (!validId) {
                return;
            }
            const observer = new MutationObserver(function(mutations) {
                let hasNewNodes = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        hasNewNodes = true;
                    }
                });
                if (hasNewNodes) {
                    window._idjs.core.processLinks(validId);
                    window._idjs.core.processForms(validId);
                }
            });
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            window._idjs.observer = observer;
        } catch (e) {
        }
    };
    window._idjs.init = function() {
        try {
            const idValue = window._idjs.core.getCurrentId();
            if (!idValue) {
                return;
            }
            const validId = window._idjs.util.validateId(idValue);
            if (!validId) {
                return;
            }
            if (window._idjs.config.updateCurrentUrl) {
                window._idjs.core.updateCurrentUrl(validId);
            }
            window._idjs.core.processLinks(validId);
            window._idjs.core.processForms(validId);
            window._idjs.observeDOM(validId);
            window._idjs.initialized = true;
        } catch (e) {
        }
    };
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', window._idjs.init);
    } else {
        window._idjs.init();
    }
})();